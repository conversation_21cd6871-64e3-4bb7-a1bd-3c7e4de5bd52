import dayjs from "dayjs";

export function parseUrlToObj(url: string): Record<string, any> {
	if (!url) {
		return {};
	}
	const res: Record<string, string | string[]> = {};
	const [, paramsStr] = url.split("?");
	if (paramsStr) {
		const kvArr = paramsStr.split("&");
		for (const kvStr of kvArr) {
			const [k, v] = kvStr.split("=");
			if (res[k]) {
				if (Array.isArray(res[k])) {
					(res[k] as string[]).push(v);
				} else {
					res[k] = [res[k], v] as string[];
				}
			} else {
				res[k] = v;
			}
		}
	}
	return res;
}

export function JsonParse<R = any>(str: string) {
	try {
		const res = JSON.parse(str) as R;
		return res;
	} catch (error) {
		console.error("JSON解析错误, 您正在试图解析 ", str.toString());
		return undefined;
	}
}
export const dateRangePrefix = [
	{
		label: "今天",
		value: [dayjs().startOf("day"), dayjs()],
	},
	{
		label: "近3天",
		value: [dayjs().add(-3, "d").startOf("days"), dayjs()],
	},
	{
		label: "近7天",
		value: [dayjs().add(-7, "d").startOf("days"), dayjs()],
	},
];

// 加密身份证号中间的内容，只保留前三位和后两位，中间使用*代替
export function encryptIdCard(idCard?: string) {
	if (!idCard || idCard.length !== 18) {
		return idCard;
	}
	return `${idCard.slice(0, 3)}${'*'.repeat(12)}${idCard.slice(-2)}`;
}

// 加密手机号中间的内容，只保留前三位和后四位，中间使用*代替
export function encryptPhone(phone?: string) {
	if (!phone || phone.length !== 11) {
		return phone;
	}
	return `${phone.slice(0, 3)}${'*'.repeat(4)}${phone.slice(-4)}`;
}

// 格式化时间：传入内容为：2025-05-13T13:59:51.620085+08:00，返回：2025-05-13 13:59:51
export function formatTime(time?: string) {
	if (!time) {
		return time;
	}
	return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
}

// 计算上网时长
export const calculateSurfingDuration = (startTime: string, stopTime?: string) => {
  if (!startTime || !stopTime) return "-";
  
  const start = dayjs(startTime);
  const end = stopTime ? dayjs(stopTime) : dayjs();
  
  const durationInSeconds = end.diff(start, "second");
  
  const hours = Math.floor(durationInSeconds / 3600);
  const minutes = Math.floor((durationInSeconds % 3600) / 60);
  const seconds = durationInSeconds % 60;
  
  let result = "";
  
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分钟`;
  }
  result += `${seconds}秒`;
  
  return result;
};

interface TimeRange {
  label: string;
  key: string;
  getTimeRange?: () => {
    startTime: string;
    endTime: string;
  };
}

export const TIME_RANGES: TimeRange[] = [
  {
    label: "最近三天",
    key: "3days",
    getTimeRange: () => ({
      startTime: dayjs().subtract(3, 'day').startOf('day').format(),
      endTime: dayjs().format(),
    }),
  },
  {
    label: "最近一周",
    key: "week",
    getTimeRange: () => ({
      startTime: dayjs().subtract(7, 'day').startOf('day').format(),
      endTime: dayjs().format(),
    }),
  },
  {
    label: "最近一月",
    key: "month",
    getTimeRange: () => ({
      startTime: dayjs().subtract(1, 'month').startOf('day').format(),
      endTime: dayjs().format(),
    }),
  },
  {
    label: "其他时间",
    key: "custom",
  },
];