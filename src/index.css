@import "tailwindcss";

@theme {
  --color-primary: #2F54D4;
  --color-important-text: #333;
  --color-second-text: #666;
  --color-des-text: #999;
  /* 分割线颜色 */
  --color-cut-off-rule: #F0F0F0; 
  --color-bg-color: #F4F4F4;
  --color-success: #01EE87;
  --color-warning: #ff9900;
  --color-error: #ff2900;
  --color-info: #f4f4f4;
}

@keyframes nroad-loading {
  100% {transform: rotate(1turn)}
}
.nroad-loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  border: 8px solid #0000;
  border-right-color: var(--color-primary);
  position: relative;
  animation: nroad-loading 1s infinite linear;
}
.nroad-loader:before,
.nroad-loader:after {
  content: "";
  position: absolute;
  inset: -8px;
  border-radius: 50%;
  border: inherit;
  animation: inherit;
  animation-duration: 4s;
}