import type React from 'react';
import { lazy, Suspense } from 'react';
import {
  createBrowserRouter,
  RouterProvider,
  type RouteObject,
} from "react-router";
import Loading from '@/components/Loading';
import PageLayout from '@/layouts/PageLayout';
import ProtectedRoute from './ProtectedRoute';

// 使用懒加载替代直接导入
const LoginPage = lazy(() => import('@/pages/login'));
const ProtectedPage = lazy(() => import('@/pages/protected'));
const AccountPage = lazy(() => import('@/pages/account'));

function createSuspenseElement(element: React.ReactNode, isProtected = false) {
  if (isProtected) {
    return (
      <ProtectedRoute>
        <Suspense fallback={<Loading />}>
          {element}
        </Suspense>
      </ProtectedRoute>
    )
  }
  return (
    <Suspense fallback={<Loading />}>
      {element}
    </Suspense>
  )
}

const publicRoutes: Array<RouteObject> = [
  {
    path: "/login",
    element: createSuspenseElement(<LoginPage />),
  },
  {
    path: "/",
    element: <PageLayout />,
    children: [
      {
        path: "protected",
        element: createSuspenseElement(<ProtectedPage />, true)
      },
      {
        path: "account",
        element: createSuspenseElement(<AccountPage />, true)
      },
    ],
  },
];

const Router: React.FC = () => {
  const router = createBrowserRouter([...publicRoutes]);
  // 可以在这里动态控制路由，比如通过接口内容来控制路由
  return (
    <RouterProvider router={router} />
  )
}

export default Router;
