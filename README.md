# 前端C端项目模板

## 主要技术栈
1. 脚手架：[vite](https://vite.dev/)
2. (请求)状态管理：[swr](https://swr.vercel.app/zh-CN), [zustand](https://zustand.docs.pmnd.rs/getting-started/introduction)
3. 样式、UI：[tailwindcss](https://tailwindcss.com/), 当前使用 antd，可按需替换
4. 路由：[react-router](https://reactrouter.com/)
5. 图标：[react-icons](https://react-icons.github.io/react-icons/)

## Why?

1. vite 相比 umi，开发启动快，配置比 webpack 简单，到 2025 年插件也非常丰富，构建产物为原生 esmodule，也可通过 legacy-plugin 降级为传统的 IIFE。
2. swr，相比 [react-query](https://tanstack.com/query/latest/docs/framework/react/overview)更加轻量，配置项少，使用简便，心智负担小；相比手动使用 Effect 实现，功能更丰富，API 设计更合理。
3. zustand 相比“化繁为简”的 redux、dva，API 简单，无需 provider。
4. tailwindcss 原子化的 css 写样式的速度更快，不用给盒子起名字，减少负担；4.0 后，不再需要使用复杂难以阅读的 `tailwind.config.js`，而是通过 css 注入自定义样式、变量；。
5. UI/组件库，一般来说，C 端项目不需要 UI/组件库，可按需选择
6. react-router，除此之外还有其他选择吗？
7. 集成了各大厂商提供的图标，引入方便，支持自定义样式，支持按需导入

## 构建优化(还请持续更新)

### vite 默认配置
构建结果如下(已经使用了路由页面懒加载)
![](./docs/images/20250609092610.jpg)

antd 支持按需导入，每个页面会分别导入自己需要的组件，如果两个页面都使用了 button，那每个页面的 js中都会有自己的 button 组件代码，也就是说，每个页面都可能比较大


### 抽离公共代码
rollup 支持自定义构建文件，可以把一些通用的库打包成一个 js，这些库的产物几乎不会发生变化，更容易在 cdn 或者用户侧进行缓存。
```ts
rollupOptions: {
  output: {
    manualChunks: {
      "nroad-core": ["react", "react-dom", "react-router"],
      "nroad-ui": [
        "antd",
        "sonner"
      ],
      "nroad-icons": ["react-icons/bi", "react-icons/fa","react-icons/pi","react-icons/tb","react-icons/vsc"],
      "nroad-utils": ["axios", "dayjs", "zustand", "crypto-js", "swr"],
    },
  },
},
```

构建结果如下
![](./docs/images/20250609092919.jpg)

有一个比较大的文件，是nroad-ui-xxx.js，这个是全量的 antd+sunner。用户下载一次，后面就无需下载，且也可通过 cdn 做缓存。
也可进行进一步优化，只打包项目内使用的组件，这会复杂一些，需要进一步通过某插件配置。



