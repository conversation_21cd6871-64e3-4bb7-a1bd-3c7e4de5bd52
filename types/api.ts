export interface Result<T = any> {
  status: number;
  message: string;
  data?: T;
}

export type PaginationRequest<T = Record<string, any>> = {
  page?: number;
  size?: number;
} & SortRequestParams &
  Partial<T>;

export interface PaginationResponse<T = any> {
  totalElements: number;
  totalPages: number;
  content: Array<T>;
}

export interface SortRequestParams {
  properties?: string[];
  direction?: "ASC" | "DESC";
}
